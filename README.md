# 🎮 Everwing Auto Clicker Chrome Extension

A Chrome extension that automatically clicks buttons in the Everwing game on Facebook, with comprehensive debugging tools and console commands.

## ✨ Features

- **Automatic Button Clicking**: Detects and clicks game buttons automatically
- **Smart Button Detection**: Recognizes various button types (Play, Continue, Collect, Claim)
- **Debugging Tools**: Comprehensive debugging interface with visual highlights
- **Console Commands**: Direct console access for testing and debugging
- **Customizable Intervals**: Adjustable click timing (100ms - 10s)
- **Visual Feedback**: Button highlighting and status indicators
- **Real-time Monitoring**: DOM change detection for dynamic content

## 📦 Installation

### Method 1: Load Unpacked Extension (Recommended for Development)

1. **Download/Clone the Extension**
   ```bash
   git clone <repository-url>
   # or download and extract the ZIP file
   ```

2. **Open Chrome Extensions Page**
   - Navigate to `chrome://extensions/`
   - Or go to Chrome Menu → More Tools → Extensions

3. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top-right corner

4. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The extension should now appear in your extensions list

5. **Pin the Extension** (Optional)
   - Click the puzzle piece icon in Chrome toolbar
   - Pin the "Everwing Auto Clicker" extension for easy access

### Method 2: Create Icons (Optional)

The extension includes an SVG icon template. For better appearance, convert to PNG:

```bash
# Convert SVG to PNG icons (requires ImageMagick or similar)
convert icons/icon.svg -resize 16x16 icons/icon16.png
convert icons/icon.svg -resize 48x48 icons/icon48.png
convert icons/icon.svg -resize 128x128 icons/icon128.png
```

## 🚀 Usage

### Basic Usage

1. **Navigate to Facebook**
   - Go to `facebook.com`
   - Open the Everwing game

2. **Open Extension Popup**
   - Click the extension icon in Chrome toolbar
   - Or use the keyboard shortcut (if configured)

3. **Configure Settings**
   - Set click interval (default: 1000ms)
   - Enable debug mode for detailed logging
   - Click "Start Auto Clicker"

4. **Monitor Activity**
   - Watch the status indicator
   - Check browser console for debug information

### Advanced Usage with Console Commands

Open Chrome DevTools (`F12`) and use these console commands:

```javascript
// Basic controls
everwingAutoClicker.start()           // Start auto clicking
everwingAutoClicker.stop()            // Stop auto clicking
everwingAutoClicker.toggle()          // Toggle on/off
everwingAutoClicker.getStatus()       // Get current status

// Configuration
everwingAutoClicker.setInterval(500)  // Set click interval to 500ms
everwingAutoClicker.setDebugMode(true) // Enable debug mode

// Button detection
everwingAutoClicker.findButtons()     // Find all clickable buttons
everwingAutoClicker.clickButton('button[aria-label="Play"]') // Click specific button

// Debug tools
EverwingDebug.highlightButtons()      // Highlight all clickable buttons
EverwingDebug.removeHighlights()      // Remove highlights
EverwingDebug.analyzePage()           // Analyze current page
EverwingDebug.testClick('selector')   // Test click on element
EverwingDebug.startMonitoring()       // Monitor DOM changes
EverwingDebug.exportState()           // Export debug state
```

## 🔧 Debugging Guide

### Using Chrome DevTools

1. **Open DevTools**
   - Press `F12` or right-click → "Inspect"
   - Go to the "Sources" tab

2. **Set Breakpoints**
   - Navigate to the extension files in Sources
   - Click line numbers to set breakpoints in:
     - `content.js` - Main auto-clicking logic
     - `debug.js` - Debug utilities
     - `popup.js` - Extension popup

3. **Debug Console Commands**
   ```javascript
   // Enable debug mode first
   everwingAutoClicker.setDebugMode(true)
   
   // Highlight all potential buttons
   EverwingDebug.highlightButtons()
   
   // Analyze the current page
   EverwingDebug.analyzePage()
   
   // Test clicking a specific element
   EverwingDebug.testClick('button[aria-label="Play"]')
   
   // Monitor for new buttons
   EverwingDebug.startMonitoring()
   ```

### Common Debugging Scenarios

#### 1. Extension Not Working
```javascript
// Check if extension loaded
console.log(window.everwingAutoClicker ? 'Loaded' : 'Not loaded')

// Check current status
everwingAutoClicker.getStatus()

// Verify page detection
EverwingDebug.analyzePage()
```

#### 2. Buttons Not Being Clicked
```javascript
// Find all buttons
const buttons = everwingAutoClicker.findButtons()
console.table(buttons)

// Highlight buttons visually
EverwingDebug.highlightButtons()

// Test manual clicking
EverwingDebug.testClick('button') // Replace with actual selector
```

#### 3. Performance Issues
```javascript
// Check click interval
everwingAutoClicker.getStatus().interval

// Reduce frequency
everwingAutoClicker.setInterval(2000) // 2 seconds

// Monitor DOM changes
EverwingDebug.startMonitoring()
```

### Debug Mode Features

When debug mode is enabled:
- Detailed console logging
- Button detection information
- Click attempt results
- Performance metrics
- Error reporting

### Visual Debugging

The extension provides visual debugging tools:
- **Button Highlighting**: Red outlines around clickable elements
- **Status Indicators**: Real-time status in popup
- **Console Logging**: Detailed operation logs

## ⚙️ Configuration

### Extension Settings

- **Click Interval**: Time between clicks (100-10000ms)
- **Debug Mode**: Enable detailed logging
- **Auto-start**: Remember last state

### Button Selectors

The extension looks for these button types:
```javascript
// ARIA labels
'button[aria-label*="Play"]'
'button[aria-label*="Continue"]'
'button[aria-label*="Collect"]'
'button[aria-label*="Claim"]'

// Role-based
'div[role="button"]'

// Class-based
'.game-button'
'.play-button'
'.continue-button'
'.collect-button'
```

### Customizing Button Detection

To add custom button selectors, modify `content.js`:

```javascript
this.buttonSelectors = [
  // Add your custom selectors here
  'button[data-testid="play-button"]',
  '.custom-game-button',
  // ... existing selectors
];
```

## 🛠️ Development

### File Structure
```
everwing-auto-clicker/
├── manifest.json          # Extension manifest
├── content.js            # Main content script
├── background.js         # Service worker
├── popup.html           # Extension popup UI
├── popup.js             # Popup functionality
├── debug.js             # Debug utilities
├── icons/               # Extension icons
│   └── icon.svg         # SVG icon template
└── README.md           # This file
```

### Making Changes

1. **Edit Files**: Modify the extension files as needed
2. **Reload Extension**: Go to `chrome://extensions/` and click reload
3. **Test Changes**: Navigate to Facebook and test functionality
4. **Debug**: Use console commands and DevTools

### Adding New Features

1. **Content Script**: Add functionality to `content.js`
2. **Debug Tools**: Add utilities to `debug.js`
3. **UI Controls**: Update `popup.html` and `popup.js`
4. **Permissions**: Update `manifest.json` if needed

## 🚨 Troubleshooting

### Common Issues

1. **Extension Not Loading**
   - Check Chrome extensions page for errors
   - Verify all files are present
   - Check manifest.json syntax

2. **No Buttons Found**
   - Enable debug mode
   - Use `EverwingDebug.analyzePage()`
   - Check if selectors match page elements

3. **Clicking Not Working**
   - Verify button is clickable
   - Check for JavaScript errors in console
   - Try different click intervals

4. **Performance Issues**
   - Increase click interval
   - Disable debug mode
   - Check for memory leaks

### Getting Help

1. **Check Console**: Look for error messages
2. **Use Debug Tools**: Run analysis commands
3. **Export State**: Use `EverwingDebug.exportState()`
4. **Check Permissions**: Verify extension has necessary permissions

## ⚠️ Disclaimer

This extension is for educational and testing purposes. Use responsibly and in accordance with Facebook's Terms of Service and the game's rules.

## 📄 License

This project is open source. Feel free to modify and distribute according to your needs.
