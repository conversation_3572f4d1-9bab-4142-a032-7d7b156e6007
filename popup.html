<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .status {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .status.enabled {
      background: rgba(76, 175, 80, 0.3);
    }
    
    .status.disabled {
      background: rgba(244, 67, 54, 0.3);
    }
    
    .controls {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 10px 15px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    button.primary {
      background: #4CAF50;
      border-color: #45a049;
    }
    
    button.primary:hover {
      background: #45a049;
    }
    
    button.danger {
      background: #f44336;
      border-color: #da190b;
    }
    
    button.danger:hover {
      background: #da190b;
    }
    
    .setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      padding: 8px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
    }
    
    .setting label {
      font-size: 13px;
      flex: 1;
    }
    
    input[type="number"] {
      width: 60px;
      padding: 4px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      text-align: center;
    }
    
    input[type="checkbox"] {
      transform: scale(1.2);
    }
    
    .debug-section {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .debug-section h3 {
      margin: 0 0 10px 0;
      font-size: 14px;
      font-weight: 500;
    }
    
    .debug-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }
    
    .debug-buttons button {
      font-size: 12px;
      padding: 6px 8px;
    }
    
    .info {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .warning {
      background: rgba(255, 193, 7, 0.2);
      border: 1px solid rgba(255, 193, 7, 0.5);
      border-radius: 4px;
      padding: 8px;
      margin: 10px 0;
      font-size: 12px;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎮 Everwing Auto Clicker</h1>
  </div>
  
  <div id="status" class="status disabled">
    <div id="statusText">Disabled</div>
    <div id="pageInfo" style="font-size: 11px; margin-top: 5px; opacity: 0.8;"></div>
  </div>
  
  <div class="controls">
    <button id="toggleBtn" class="primary">Start Auto Clicker</button>
    
    <div class="setting">
      <label for="intervalInput">Click Interval (ms):</label>
      <input type="number" id="intervalInput" min="100" max="10000" step="100" value="1000">
    </div>
    
    <div class="setting">
      <label for="debugMode">Debug Mode:</label>
      <input type="checkbox" id="debugMode">
    </div>
  </div>
  
  <div class="debug-section">
    <h3>🔧 Debug Tools</h3>
    <div class="debug-buttons">
      <button id="highlightBtn">Highlight Buttons</button>
      <button id="analyzeBtn">Analyze Page</button>
      <button id="findBtn">Find Buttons</button>
      <button id="exportBtn">Export State</button>
    </div>
  </div>
  
  <div class="warning" id="warning" style="display: none;">
    ⚠️ Navigate to Facebook Everwing game to use this extension
  </div>
  
  <div class="info">
    <div>Console Commands Available:</div>
    <div style="font-family: monospace; font-size: 10px; margin-top: 5px;">
      everwingAutoClicker.toggle()<br>
      EverwingDebug.highlightButtons()
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
