# 🛑 Everwing Game Debugging Guide

This guide will help you set breakpoints in the Everwing game's JavaScript, specifically in the `sendNotification` function.

## 🎯 Target Code Location

**File:** `https://apps-141184676316522.apps.fbsbx.com/br-compress-instant-bundle/br/1174389249249108/30099212706344229/0.js`

**Target Function:**
```javascript
t.sendNotification = function(e, t, i) {
    var n = this;
    (i || "debug" !== Ze || console.log("Sending Notification : " + e, t),
    this.observerMap[e]) && this.observerMap[e].slice().forEach((function(i) {
        i.handler.call(i.context, e, t),
        i.once && n.removeObserver(e, i.context)
    }))
}
```

**Target Line for Breakpoint:**
```javascript
this.observerMap[e]) && this.observerMap[e].slice().forEach((function(i) {
```

## 🔧 Method 1: Automatic Breakpoint (Recommended)

### Step 1: Load the Extension
1. Install the Everwing Auto Clicker extension
2. Navigate to Facebook Everwing game
3. Open Chrome DevTools (`F12`)

### Step 2: Enable Automatic Breakpoint
```javascript
// In the console, run:
EverwingBreakpoint.enable()
```

### Step 3: Trigger the Notification
- Play the game normally
- The breakpoint will automatically trigger when `sendNotification` is called
- Execution will pause at the `debugger;` statement

### Step 4: Navigate to Target Line
1. When the debugger pauses, you'll be in the wrapped function
2. Press `F11` (Step Into) to enter the original `sendNotification`
3. Continue stepping until you reach the target line

## 🔧 Method 2: Manual Breakpoint in DevTools

### Step 1: Open DevTools Sources
1. Press `F12` to open DevTools
2. Go to the **Sources** tab
3. Press `Ctrl+P` (or `Cmd+P` on Mac) to open file search

### Step 2: Find the Game Script
1. Type `0.js` in the search box
2. Select the file: `apps-141184676316522.apps.fbsbx.com/.../0.js`
3. The minified game script will open

### Step 3: Locate sendNotification
1. Press `Ctrl+F` to search within the file
2. Search for: `sendNotification`
3. Look for the function definition with `observerMap`

### Step 4: Set Breakpoint
1. Find the line: `this.observerMap[e]) && this.observerMap[e].slice().forEach`
2. Click on the line number to set a breakpoint
3. The line should highlight in blue

### Step 5: Trigger the Breakpoint
- Play the game to trigger notifications
- Execution will pause at your breakpoint

## 🔍 Method 3: Conditional Breakpoint

### Step 1: Set Conditional Breakpoint
1. Right-click on the line number where you want the breakpoint
2. Select "Add conditional breakpoint"
3. Enter condition: `e === 'specific_event_name'` (replace with actual event)

### Step 2: Monitor Specific Events
```javascript
// First, discover what events are being sent:
EverwingDebug.setupGameDebug()

// Check console for notification logs to see event names
// Then set conditional breakpoint for specific events
```

## 🎮 Using Extension Debug Features

### Console Commands Available:

```javascript
// Breakpoint controls
EverwingBreakpoint.enable()     // Enable automatic breakpoint
EverwingBreakpoint.disable()    // Disable breakpoint
EverwingBreakpoint.toggle()     // Toggle breakpoint on/off
EverwingBreakpoint.status()     // Check if breakpoint is enabled

// Game analysis
EverwingDebug.setupGameDebug()      // Setup game debugging
EverwingDebug.analyzeGameScript()   // Analyze the game script
EverwingDebug.analyzePage()         // Analyze current page

// General debugging
EverwingDebug.highlightButtons()    // Highlight clickable elements
EverwingDebug.exportState()         // Export debug information
```

## 📊 Debugging Information

When the breakpoint triggers, you can inspect:

### Variables Available:
- `e` - Event name/type
- `t` - Event data/payload  
- `i` - Additional parameter
- `this.observerMap` - Map of event observers
- `n` - Reference to `this` context

### Useful Console Commands During Breakpoint:
```javascript
// Inspect the current event
console.log('Event:', e)
console.log('Data:', t)
console.log('Observers for this event:', this.observerMap[e])

// See all registered observers
console.log('All observers:', this.observerMap)

// Check observer details
this.observerMap[e].forEach((observer, index) => {
    console.log(`Observer ${index}:`, observer)
})
```

## 🚨 Troubleshooting

### Breakpoint Not Triggering
1. **Check if game is loaded:**
   ```javascript
   EverwingDebug.analyzePage()
   ```

2. **Verify extension is working:**
   ```javascript
   console.log(window.EverwingDebug ? 'Debug loaded' : 'Debug not loaded')
   ```

3. **Check for sendNotification:**
   ```javascript
   EverwingDebug.setupGameDebug()
   ```

### Script Not Found
1. **Refresh the page** - Script URLs may change
2. **Check Network tab** in DevTools for actual script URLs
3. **Use extension's script analysis:**
   ```javascript
   EverwingDebug.analyzeGameScript()
   ```

### Minified Code Issues
1. **Enable source maps** if available in DevTools settings
2. **Use "Pretty print"** button `{}` in Sources tab
3. **Search for patterns** instead of exact function names

## 🎯 Advanced Debugging Techniques

### 1. Monitor All Notifications
```javascript
// Enable debug mode to see all notifications
EverwingBreakpoint.enable()

// Or log without breaking
EverwingDebug.setupGameDebug()
```

### 2. Filter Specific Events
```javascript
// Set conditional breakpoint for specific events
// Condition: e === 'game_start' || e === 'level_complete'
```

### 3. Inspect Observer Patterns
```javascript
// During breakpoint, analyze observer patterns
Object.keys(this.observerMap).forEach(eventType => {
    console.log(`Event: ${eventType}, Observers: ${this.observerMap[eventType].length}`)
})
```

### 4. Modify Behavior (Advanced)
```javascript
// During breakpoint, you can modify the data
t.modified = true
console.log('Modified event data:', t)
```

## 📝 Notes

- The game script is minified, so variable names are shortened
- Script URLs may change between game updates
- Use the extension's automatic detection for best results
- Always test in a safe environment first

## 🔗 Related Files

- `debug.js` - Contains the automatic breakpoint logic
- `content.js` - Main extension functionality
- `popup.js` - Extension UI controls

Happy debugging! 🎮🐛
