# 🔧 Event Modification Guide

This guide shows you how to modify event data when the breakpoint is triggered in the Everwing game's `sendNotification` function.

## 🎯 Target Event Example

When you hit the breakpoint, you'll see event data like this:
```javascript
e = "piggyBank"
t = {
    "type": "piggyBank", 
    "currency": "usd",
    "itemID": "com.everwinggame.purchase.piggy_1",
    "gemCount": 360
}
```

## 🚀 Quick Commands (During Breakpoint)

### Method 1: Direct Modification (Simplest)
```javascript
// Modify currency directly
e.currency = null

// Modify other properties
t.currency = null
t.gemCount = 500
t.itemID = "modified_item"
```

### Method 2: Helper Functions (Recommended)
```javascript
// Show current event data
ModifyEvent.showCurrent()

// Modify specific properties
ModifyEvent.setCurrency(null)
ModifyEvent.setGems(500)
ModifyEvent.setItemID("new_item_id")

// Modify any property
ModifyEvent.set("currency", null)
ModifyEvent.set("gemCount", 1000)

// See what you've changed
ModifyEvent.showChanges()

// Reset if you made a mistake
ModifyEvent.reset()
```

## 📋 Step-by-Step Process

### 1. Setup Breakpoint
```javascript
// Enable the breakpoint
EverwingBreakpoint.enable()
```

### 2. Trigger the Event
- Play the game normally
- Perform actions that trigger piggyBank events
- The debugger will pause at the target line

### 3. Modify Event Data
When the breakpoint hits, you'll see:
```
🛑 BREAKPOINT: About to execute observerMap forEach
📝 Event data available in scope as "e" and "t"
🔧 Global access: window.currentEventData
⚡ Quick commands:
   - ModifyEvent.setCurrency(null)
   - ModifyEvent.setGems(500)
   - ModifyEvent.showCurrent()
   - ModifyEvent.reset()
```

### 4. Execute Modification Commands
```javascript
// Option A: Direct modification
t.currency = null

// Option B: Using helper
ModifyEvent.setCurrency(null)

// Verify the change
console.log('Modified currency:', t.currency)
```

### 5. Continue Execution
- Press `F8` (Continue) or click the play button
- The game will receive your modified event data

## 🔧 Available Modification Commands

### Currency Modifications
```javascript
// Set currency to null
ModifyEvent.setCurrency(null)
t.currency = null

// Set currency to different value
ModifyEvent.setCurrency("eur")
t.currency = "eur"

// Remove currency property entirely
delete t.currency
```

### Gem Count Modifications
```javascript
// Set gem count
ModifyEvent.setGems(1000)
t.gemCount = 1000

// Double the gems
t.gemCount = t.gemCount * 2

// Set to zero
ModifyEvent.setGems(0)
```

### Item ID Modifications
```javascript
// Change item ID
ModifyEvent.setItemID("free_item")
t.itemID = "free_item"

// Remove item ID
delete t.itemID
```

### Advanced Modifications
```javascript
// Add new properties
t.modified = true
t.timestamp = Date.now()

// Modify nested objects (if any)
if (t.details) {
    t.details.price = 0
}

// Convert to different event type
t.type = "freeReward"
```

## 🔍 Debugging Commands

### Inspect Current Data
```javascript
// Show all current event data
ModifyEvent.showCurrent()

// Show just the data object
console.log('Event data (t):', t)

// Show event type
console.log('Event type (e):', e)
```

### Compare Changes
```javascript
// See what you've modified
ModifyEvent.showChanges()

// Access original data
console.log('Original:', ModifyEvent.current.originalT)
console.log('Current:', ModifyEvent.current.t)
```

### Reset and Retry
```javascript
// Reset to original values
ModifyEvent.reset()

// Make different modifications
ModifyEvent.setCurrency("free")
ModifyEvent.setGems(9999)
```

## 🎮 Common Use Cases

### 1. Make Purchases Free
```javascript
// When piggyBank event triggers
t.currency = null
// or
t.currency = "free"
```

### 2. Increase Rewards
```javascript
// Increase gem rewards
t.gemCount = t.gemCount * 10

// Set specific high value
ModifyEvent.setGems(9999)
```

### 3. Change Item Types
```javascript
// Change to premium item
t.itemID = "com.everwinggame.purchase.premium_pack"

// Change event type entirely
t.type = "dailyReward"
```

### 4. Add Debug Information
```javascript
// Add tracking
t.modified = true
t.modifiedAt = new Date().toISOString()
t.originalCurrency = ModifyEvent.current.originalT.currency
```

## ⚠️ Important Notes

### Scope Availability
- `e` and `t` are only available **during the breakpoint**
- Use `ModifyEvent` helpers for global access
- `window.currentEventData` stores the current event globally

### Timing
- Modifications must be made **before** continuing execution
- Once you press F8 (Continue), the modified data is sent to the game
- You cannot modify data after the breakpoint continues

### Safety
- Always test modifications in a safe environment
- Use `ModifyEvent.reset()` if you make mistakes
- Keep track of original values with `ModifyEvent.showChanges()`

## 🚨 Troubleshooting

### Breakpoint Not Hitting
```javascript
// Check if breakpoint is enabled
EverwingBreakpoint.status()

// Re-enable if needed
EverwingBreakpoint.enable()

// Check if game debugging is setup
EverwingDebug.setupGameDebug()
```

### Variables Not Available
```javascript
// Check if currentEventData exists
console.log(window.currentEventData)

// Use global access instead
ModifyEvent.showCurrent()
```

### Modifications Not Working
```javascript
// Verify the change was applied
console.log('Current t:', t)

// Check if you're modifying the right object
ModifyEvent.showChanges()

// Make sure you continue execution after modifying
// Press F8 or click Continue button
```

## 📝 Example Session

```javascript
// 1. Enable breakpoint
EverwingBreakpoint.enable()

// 2. Play game until breakpoint hits
// Console shows: "🛑 BREAKPOINT: About to execute observerMap forEach"

// 3. Check current data
ModifyEvent.showCurrent()
// Shows: Event (e): "piggyBank", Data (t): {type: "piggyBank", currency: "usd", ...}

// 4. Modify currency
t.currency = null
// or
ModifyEvent.setCurrency(null)

// 5. Verify change
console.log('Modified currency:', t.currency)  // null

// 6. Continue execution (F8)
// Game receives modified event with currency = null
```

Happy modifying! 🎮✨
