// Debug script injected into page context for enhanced debugging
(function() {
  'use strict';
  
  // Enhanced debugging utilities for Everwing Auto Clicker
  window.EverwingDebug = {
    // Highlight all potential clickable elements
    highlightButtons: function() {
      const selectors = [
        'button[aria-label*="Play"]',
        'button[aria-label*="Continue"]',
        'button[aria-label*="Collect"]',
        'button[aria-label*="Claim"]',
        'div[role="button"]',
        '.game-button',
        '.play-button',
        '.continue-button',
        '.collect-button'
      ];
      
      // Remove existing highlights
      document.querySelectorAll('.everwing-debug-highlight').forEach(el => {
        el.classList.remove('everwing-debug-highlight');
      });
      
      // Add CSS for highlighting if not exists
      if (!document.getElementById('everwing-debug-styles')) {
        const style = document.createElement('style');
        style.id = 'everwing-debug-styles';
        style.textContent = `
          .everwing-debug-highlight {
            outline: 3px solid #ff0000 !important;
            background-color: rgba(255, 0, 0, 0.1) !important;
            position: relative !important;
          }
          .everwing-debug-highlight::after {
            content: 'AUTO-CLICK TARGET';
            position: absolute;
            top: -20px;
            left: 0;
            background: #ff0000;
            color: white;
            padding: 2px 5px;
            font-size: 10px;
            font-weight: bold;
            z-index: 10000;
          }
        `;
        document.head.appendChild(style);
      }
      
      let count = 0;
      selectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
          if (this.isElementInteractable(el)) {
            el.classList.add('everwing-debug-highlight');
            count++;
          }
        });
      });
      
      console.log(`Highlighted ${count} potential clickable elements`);
      return count;
    },
    
    // Remove highlights
    removeHighlights: function() {
      document.querySelectorAll('.everwing-debug-highlight').forEach(el => {
        el.classList.remove('everwing-debug-highlight');
      });
      console.log('Removed all highlights');
    },
    
    // Analyze page for game elements
    analyzePage: function() {
      const analysis = {
        url: window.location.href,
        title: document.title,
        isEverwingPage: this.isEverwingPage(),
        gameElements: this.findGameElements(),
        clickableButtons: this.findAllClickableElements(),
        timestamp: new Date().toISOString()
      };
      
      console.table(analysis.clickableButtons);
      console.log('Page Analysis:', analysis);
      return analysis;
    },
    
    // Check if current page is Everwing
    isEverwingPage: function() {
      const indicators = [
        'everwing',
        'game',
        'play',
        'facebook.com/instantgames'
      ];
      
      const pageText = document.body.textContent.toLowerCase();
      const url = window.location.href.toLowerCase();
      
      return indicators.some(indicator => 
        pageText.includes(indicator) || url.includes(indicator)
      );
    },
    
    // Find all game-related elements
    findGameElements: function() {
      const gameSelectors = [
        'canvas',
        '[class*="game"]',
        '[id*="game"]',
        '[class*="play"]',
        '[id*="play"]',
        'iframe[src*="game"]',
        'iframe[src*="play"]'
      ];
      
      const elements = [];
      gameSelectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
          elements.push({
            tagName: el.tagName,
            className: el.className,
            id: el.id,
            selector: selector
          });
        });
      });
      
      return elements;
    },
    
    // Find all clickable elements with details
    findAllClickableElements: function() {
      const elements = [];
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach(el => {
        if (this.isElementInteractable(el)) {
          elements.push({
            tagName: el.tagName,
            text: el.textContent?.trim().substring(0, 50) || '',
            ariaLabel: el.getAttribute('aria-label') || '',
            className: el.className,
            id: el.id,
            role: el.getAttribute('role') || '',
            visible: this.isElementVisible(el),
            clickable: true
          });
        }
      });
      
      return elements;
    },
    
    // Test click on element by selector
    testClick: function(selector) {
      const element = document.querySelector(selector);
      if (!element) {
        console.error('Element not found:', selector);
        return false;
      }
      
      console.log('Testing click on:', element);
      
      // Highlight element temporarily
      const originalOutline = element.style.outline;
      element.style.outline = '3px solid #00ff00';
      
      setTimeout(() => {
        element.style.outline = originalOutline;
      }, 2000);
      
      // Perform click
      element.click();
      
      // Dispatch additional events
      ['mousedown', 'mouseup', 'click'].forEach(eventType => {
        const event = new MouseEvent(eventType, {
          bubbles: true,
          cancelable: true,
          view: window
        });
        element.dispatchEvent(event);
      });
      
      console.log('Click test completed');
      return true;
    },
    
    // Monitor DOM changes for new buttons
    startMonitoring: function() {
      if (this.observer) {
        this.stopMonitoring();
      }
      
      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const buttons = node.querySelectorAll ? 
                  node.querySelectorAll('button, [role="button"], .game-button, .play-button') : [];
                
                if (buttons.length > 0) {
                  console.log('New buttons detected:', buttons);
                }
              }
            });
          }
        });
      });
      
      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      console.log('DOM monitoring started');
    },
    
    // Stop monitoring DOM changes
    stopMonitoring: function() {
      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
        console.log('DOM monitoring stopped');
      }
    },
    
    // Utility functions
    isElementInteractable: function(element) {
      return element && 
             !element.disabled && 
             element.offsetParent !== null &&
             (element.tagName === 'BUTTON' || 
              element.getAttribute('role') === 'button' ||
              element.onclick ||
              element.classList.contains('clickable') ||
              element.classList.contains('game-button') ||
              element.classList.contains('play-button') ||
              element.classList.contains('continue-button') ||
              element.classList.contains('collect-button')) &&
             getComputedStyle(element).pointerEvents !== 'none';
    },
    
    isElementVisible: function(element) {
      const rect = element.getBoundingClientRect();
      return rect.width > 0 && 
             rect.height > 0 && 
             rect.top >= 0 && 
             rect.left >= 0 &&
             getComputedStyle(element).visibility !== 'hidden' &&
             getComputedStyle(element).display !== 'none';
    },
    
    // Export current state for debugging
    exportState: function() {
      const state = {
        analysis: this.analyzePage(),
        buttons: this.findAllClickableElements(),
        gameElements: this.findGameElements(),
        timestamp: new Date().toISOString()
      };

      console.log('Exported state:', state);

      // Copy to clipboard if possible
      if (navigator.clipboard) {
        navigator.clipboard.writeText(JSON.stringify(state, null, 2))
          .then(() => console.log('State copied to clipboard'))
          .catch(err => console.log('Could not copy to clipboard:', err));
      }

      return state;
    },

    // Game-specific debugging for Everwing notification system
    setupGameDebug: function() {
      console.log('🎮 Setting up Everwing game debugging...');

      // Try to find and patch the sendNotification function
      this.patchSendNotification();

      // Set up automatic breakpoint detection
      this.setupBreakpointHelper();

      // Monitor for game script loading
      this.monitorGameScripts();
    },

    // Patch the game's sendNotification function for debugging
    patchSendNotification: function() {
      // Wait for game to load and try to find the notification system
      const checkForGame = () => {
        // Look for common game objects that might contain sendNotification
        const possibleGameObjects = [
          window.game,
          window.Game,
          window.gameInstance,
          window.everwing,
          window.Everwing
        ];

        let found = false;
        possibleGameObjects.forEach(obj => {
          if (obj && typeof obj === 'object') {
            this.searchForSendNotification(obj, 'window.' + obj.constructor.name);
            found = true;
          }
        });

        if (!found) {
          // Search in all global objects
          Object.keys(window).forEach(key => {
            if (typeof window[key] === 'object' && window[key] !== null) {
              this.searchForSendNotification(window[key], 'window.' + key);
            }
          });
        }
      };

      // Check immediately and after delays
      checkForGame();
      setTimeout(checkForGame, 2000);
      setTimeout(checkForGame, 5000);
    },

    // Recursively search for sendNotification function
    searchForSendNotification: function(obj, path, depth = 0) {
      if (depth > 3 || !obj || typeof obj !== 'object') return;

      try {
        Object.keys(obj).forEach(key => {
          if (key === 'sendNotification' && typeof obj[key] === 'function') {
            console.log(`🎯 Found sendNotification at: ${path}.${key}`);
            this.wrapSendNotification(obj, key, path);
          } else if (typeof obj[key] === 'object' && obj[key] !== null && depth < 3) {
            this.searchForSendNotification(obj[key], `${path}.${key}`, depth + 1);
          }
        });
      } catch (e) {
        // Ignore access errors
      }
    },

    // Wrap the sendNotification function with debugging
    wrapSendNotification: function(obj, methodName, path) {
      const original = obj[methodName];
      const self = this;

      obj[methodName] = function(e, t, i) {
        console.log(`🔔 sendNotification called:`, {
          event: e,
          data: t,
          param3: i,
          path: path,
          stack: new Error().stack
        });

        // Set breakpoint flag for the specific line you want to debug
        if (self.breakpointEnabled) {
          console.log('🛑 BREAKPOINT: About to execute observerMap forEach');
          debugger; // This will trigger the breakpoint
        }

        // Call original function
        return original.call(this, e, t, i);
      };

      console.log(`✅ Wrapped sendNotification at ${path}.${methodName}`);
    },

    // Helper to set up breakpoint controls
    setupBreakpointHelper: function() {
      this.breakpointEnabled = false;

      // Add global controls
      window.EverwingBreakpoint = {
        enable: () => {
          this.breakpointEnabled = true;
          console.log('🛑 Breakpoint enabled for sendNotification');
        },
        disable: () => {
          this.breakpointEnabled = false;
          console.log('✅ Breakpoint disabled for sendNotification');
        },
        toggle: () => {
          this.breakpointEnabled = !this.breakpointEnabled;
          console.log(`🔄 Breakpoint ${this.breakpointEnabled ? 'enabled' : 'disabled'}`);
        },
        status: () => {
          console.log(`Breakpoint status: ${this.breakpointEnabled ? 'enabled' : 'disabled'}`);
          return this.breakpointEnabled;
        }
      };
    },

    // Monitor for game script loading
    monitorGameScripts: function() {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.tagName === 'SCRIPT' && node.src &&
                (node.src.includes('fbsbx.com') || node.src.includes('everwing'))) {
              console.log('🎮 Game script detected:', node.src);

              node.onload = () => {
                console.log('📜 Game script loaded, searching for sendNotification...');
                setTimeout(() => this.patchSendNotification(), 1000);
              };
            }
          });
        });
      });

      observer.observe(document.head, { childList: true });
      observer.observe(document.body, { childList: true });
    },

    // Manual script analysis for the specific file you mentioned
    analyzeGameScript: function() {
      const scriptUrl = 'https://apps-141184676316522.apps.fbsbx.com/br-compress-instant-bundle/br/1174389249249108/30099212706344229/0.js';

      console.log('🔍 Analyzing game script...');
      console.log('Script URL:', scriptUrl);
      console.log('To set breakpoint manually:');
      console.log('1. Open DevTools (F12)');
      console.log('2. Go to Sources tab');
      console.log('3. Press Ctrl+P and search for "0.js"');
      console.log('4. Search for "sendNotification" in the file');
      console.log('5. Look for the line: this.observerMap[e]) && this.observerMap[e].slice().forEach');
      console.log('6. Click the line number to set breakpoint');

      // Try to access the script content if possible
      fetch(scriptUrl)
        .then(response => response.text())
        .then(content => {
          console.log('📄 Script content loaded, searching for sendNotification...');
          const lines = content.split('\n');
          lines.forEach((line, index) => {
            if (line.includes('sendNotification') && line.includes('observerMap')) {
              console.log(`🎯 Found target line at ${index + 1}:`, line.trim());
            }
          });
        })
        .catch(err => {
          console.log('❌ Could not fetch script content (CORS):', err.message);
          console.log('💡 Use DevTools Sources tab to manually locate the file');
        });
    }
  };
  
  // Initialize game debugging
  window.EverwingDebug.setupGameDebug();

  // Make debug functions available in console
  console.log('🎮 Everwing Debug Tools Loaded!');
  console.log('Available commands:');
  console.log('- EverwingDebug.highlightButtons() - Highlight all clickable buttons');
  console.log('- EverwingDebug.removeHighlights() - Remove highlights');
  console.log('- EverwingDebug.analyzePage() - Analyze current page');
  console.log('- EverwingDebug.testClick(selector) - Test click on element');
  console.log('- EverwingDebug.startMonitoring() - Monitor DOM changes');
  console.log('- EverwingDebug.stopMonitoring() - Stop monitoring');
  console.log('- EverwingDebug.exportState() - Export debug state');
  console.log('- EverwingDebug.setupGameDebug() - Setup game-specific debugging');
  console.log('- EverwingDebug.analyzeGameScript() - Analyze game script for breakpoints');
  console.log('');
  console.log('🛑 Breakpoint Controls:');
  console.log('- EverwingBreakpoint.enable() - Enable breakpoint in sendNotification');
  console.log('- EverwingBreakpoint.disable() - Disable breakpoint');
  console.log('- EverwingBreakpoint.toggle() - Toggle breakpoint');
  console.log('- EverwingBreakpoint.status() - Check breakpoint status');
  
})();
