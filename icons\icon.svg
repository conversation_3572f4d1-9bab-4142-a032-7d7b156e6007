<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- Game controller icon -->
  <g transform="translate(64,64)">
    <!-- Controller body -->
    <rect x="-24" y="-12" width="48" height="24" rx="12" fill="#fff" opacity="0.9"/>
    
    <!-- D-pad -->
    <rect x="-18" y="-3" width="8" height="2" fill="#333"/>
    <rect x="-15" y="-6" width="2" height="8" fill="#333"/>
    
    <!-- Action buttons -->
    <circle cx="12" cy="-6" r="3" fill="#333"/>
    <circle cx="18" cy="0" r="3" fill="#333"/>
    <circle cx="12" cy="6" r="3" fill="#333"/>
    <circle cx="6" cy="0" r="3" fill="#333"/>
    
    <!-- Click indicator -->
    <circle cx="0" cy="20" r="4" fill="#4CAF50" opacity="0.8">
      <animate attributeName="r" values="4;8;4" dur="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Auto text -->
  <text x="64" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#fff">AUTO</text>
</svg>
