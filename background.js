// Background service worker for Everwing Auto Clicker
chrome.runtime.onInstalled.addListener(() => {
  console.log('Everwing Auto Clicker installed');
  
  // Set default settings
  chrome.storage.sync.set({
    enabled: false,
    debugMode: false,
    clickInterval: 1000
  });
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Open popup (this is handled by manifest.json action.default_popup)
});

// Handle messages from content script or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getTabInfo') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        sendResponse({
          url: tabs[0].url,
          title: tabs[0].title,
          isEverwingPage: tabs[0].url.includes('facebook.com')
        });
      }
    });
    return true; // Keep message channel open for async response
  }
});

// Optional: Add context menu for quick access
chrome.contextMenus.create({
  id: 'everwing-toggle',
  title: 'Toggle Everwing Auto Clicker',
  contexts: ['page'],
  documentUrlPatterns: ['*://*.facebook.com/*']
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'everwing-toggle') {
    chrome.tabs.sendMessage(tab.id, { action: 'toggle' });
  }
});
