// Everwing Auto Clicker Content Script
class EverwingAutoClicker {
  constructor() {
    this.isEnabled = false;
    this.debugMode = false;
    this.clickInterval = 1000; // Default 1 second
    this.intervalId = null;
    this.buttonSelectors = [
      // Common Everwing button selectors
      'button[aria-label*="Play"]',
      'button[aria-label*="Continue"]',
      'button[aria-label*="Collect"]',
      'button[aria-label*="Claim"]',
      'div[role="button"]:contains("Play")',
      'div[role="button"]:contains("Continue")',
      'div[role="button"]:contains("Collect")',
      'div[role="button"]:contains("Claim")',
      // Generic game buttons
      '.game-button',
      '.play-button',
      '.continue-button',
      '.collect-button'
    ];
    
    this.init();
  }

  init() {
    this.log('Everwing Auto Clicker initialized');
    this.setupMessageListener();
    this.injectDebugScript();
    this.setupConsoleCommands();
    
    // Load saved settings
    chrome.storage.sync.get(['enabled', 'debugMode', 'clickInterval'], (result) => {
      this.isEnabled = result.enabled || false;
      this.debugMode = result.debugMode || false;
      this.clickInterval = result.clickInterval || 1000;
      
      if (this.isEnabled) {
        this.start();
      }
    });
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'toggle':
          this.toggle();
          sendResponse({ enabled: this.isEnabled });
          break;
        case 'setDebugMode':
          this.debugMode = request.enabled;
          chrome.storage.sync.set({ debugMode: this.debugMode });
          sendResponse({ debugMode: this.debugMode });
          break;
        case 'setInterval':
          this.clickInterval = request.interval;
          chrome.storage.sync.set({ clickInterval: this.clickInterval });
          if (this.isEnabled) {
            this.restart();
          }
          sendResponse({ interval: this.clickInterval });
          break;
        case 'getStatus':
          sendResponse({
            enabled: this.isEnabled,
            debugMode: this.debugMode,
            interval: this.clickInterval
          });
          break;
      }
    });
  }

  injectDebugScript() {
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('debug.js');
    script.onload = function() {
      this.remove();
    };
    (document.head || document.documentElement).appendChild(script);
  }

  setupConsoleCommands() {
    // Make functions available globally for console debugging
    window.everwingAutoClicker = {
      start: () => this.start(),
      stop: () => this.stop(),
      toggle: () => this.toggle(),
      setDebugMode: (enabled) => this.setDebugMode(enabled),
      setInterval: (ms) => this.setClickInterval(ms),
      findButtons: () => this.findClickableButtons(),
      clickButton: (selector) => this.clickButton(selector),
      getStatus: () => ({
        enabled: this.isEnabled,
        debugMode: this.debugMode,
        interval: this.clickInterval
      })
    };
    
    this.log('Console commands available: everwingAutoClicker.start(), .stop(), .toggle(), .setDebugMode(true/false), .setInterval(ms), .findButtons(), .clickButton(selector), .getStatus()');
  }

  start() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    this.isEnabled = true;
    chrome.storage.sync.set({ enabled: true });
    
    this.intervalId = setInterval(() => {
      this.findAndClickButton();
    }, this.clickInterval);
    
    this.log('Auto clicker started');
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.isEnabled = false;
    chrome.storage.sync.set({ enabled: false });
    
    this.log('Auto clicker stopped');
  }

  toggle() {
    if (this.isEnabled) {
      this.stop();
    } else {
      this.start();
    }
  }

  restart() {
    if (this.isEnabled) {
      this.stop();
      this.start();
    }
  }

  setDebugMode(enabled) {
    this.debugMode = enabled;
    chrome.storage.sync.set({ debugMode: enabled });
    this.log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  setClickInterval(ms) {
    this.clickInterval = ms;
    chrome.storage.sync.set({ clickInterval: ms });
    this.log(`Click interval set to ${ms}ms`);
    
    if (this.isEnabled) {
      this.restart();
    }
  }

  findClickableButtons() {
    const buttons = [];
    
    for (const selector of this.buttonSelectors) {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        if (this.isElementClickable(el)) {
          buttons.push({
            element: el,
            selector: selector,
            text: el.textContent?.trim() || el.getAttribute('aria-label') || 'No text',
            visible: this.isElementVisible(el)
          });
        }
      });
    }
    
    if (this.debugMode) {
      this.log('Found clickable buttons:', buttons);
    }
    
    return buttons;
  }

  findAndClickButton() {
    const buttons = this.findClickableButtons();
    
    // Priority order: visible buttons first
    const visibleButtons = buttons.filter(b => b.visible);
    const targetButton = visibleButtons.length > 0 ? visibleButtons[0] : buttons[0];
    
    if (targetButton) {
      this.clickButton(targetButton.element);
      return true;
    }
    
    if (this.debugMode) {
      this.log('No clickable buttons found');
    }
    
    return false;
  }

  clickButton(element) {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (!element) {
      this.log('Button element not found');
      return false;
    }
    
    try {
      // Multiple click methods for better compatibility
      element.click();
      
      // Dispatch mouse events
      const mouseEvents = ['mousedown', 'mouseup', 'click'];
      mouseEvents.forEach(eventType => {
        const event = new MouseEvent(eventType, {
          bubbles: true,
          cancelable: true,
          view: window
        });
        element.dispatchEvent(event);
      });
      
      this.log(`Clicked button: ${element.textContent?.trim() || element.getAttribute('aria-label') || 'Unknown'}`);
      return true;
    } catch (error) {
      this.log('Error clicking button:', error);
      return false;
    }
  }

  isElementClickable(element) {
    return element && 
           !element.disabled && 
           element.offsetParent !== null &&
           getComputedStyle(element).pointerEvents !== 'none';
  }

  isElementVisible(element) {
    const rect = element.getBoundingClientRect();
    return rect.width > 0 && 
           rect.height > 0 && 
           rect.top >= 0 && 
           rect.left >= 0 &&
           getComputedStyle(element).visibility !== 'hidden' &&
           getComputedStyle(element).display !== 'none';
  }

  log(...args) {
    if (this.debugMode) {
      console.log('[Everwing Auto Clicker]', ...args);
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new EverwingAutoClicker();
  });
} else {
  new EverwingAutoClicker();
}
