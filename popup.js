// Popup script for Everwing Auto Clicker
document.addEventListener('DOMContentLoaded', function() {
  const toggleBtn = document.getElementById('toggleBtn');
  const statusDiv = document.getElementById('status');
  const statusText = document.getElementById('statusText');
  const pageInfo = document.getElementById('pageInfo');
  const intervalInput = document.getElementById('intervalInput');
  const debugModeCheckbox = document.getElementById('debugMode');
  const warning = document.getElementById('warning');
  
  // Debug buttons
  const highlightBtn = document.getElementById('highlightBtn');
  const analyzeBtn = document.getElementById('analyzeBtn');
  const findBtn = document.getElementById('findBtn');
  const exportBtn = document.getElementById('exportBtn');
  
  let currentTab = null;
  
  // Initialize popup
  init();
  
  async function init() {
    try {
      // Get current tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      currentTab = tabs[0];
      
      // Check if we're on Facebook
      const isFacebookPage = currentTab.url.includes('facebook.com');
      
      if (!isFacebookPage) {
        showWarning();
        disableControls();
        return;
      }
      
      // Update page info
      pageInfo.textContent = `${currentTab.title.substring(0, 30)}...`;
      
      // Get current status from content script
      const response = await sendMessageToTab({ action: 'getStatus' });
      if (response) {
        updateUI(response);
      }
      
    } catch (error) {
      console.error('Error initializing popup:', error);
      showWarning();
    }
  }
  
  function showWarning() {
    warning.style.display = 'block';
    statusText.textContent = 'Not on Facebook';
    statusDiv.className = 'status disabled';
  }
  
  function disableControls() {
    toggleBtn.disabled = true;
    intervalInput.disabled = true;
    debugModeCheckbox.disabled = true;
    highlightBtn.disabled = true;
    analyzeBtn.disabled = true;
    findBtn.disabled = true;
    exportBtn.disabled = true;
  }
  
  function updateUI(status) {
    // Update toggle button
    if (status.enabled) {
      toggleBtn.textContent = 'Stop Auto Clicker';
      toggleBtn.className = 'danger';
      statusText.textContent = 'Active';
      statusDiv.className = 'status enabled';
    } else {
      toggleBtn.textContent = 'Start Auto Clicker';
      toggleBtn.className = 'primary';
      statusText.textContent = 'Disabled';
      statusDiv.className = 'status disabled';
    }
    
    // Update settings
    intervalInput.value = status.interval || 1000;
    debugModeCheckbox.checked = status.debugMode || false;
  }
  
  async function sendMessageToTab(message) {
    try {
      if (!currentTab) return null;
      return await chrome.tabs.sendMessage(currentTab.id, message);
    } catch (error) {
      console.error('Error sending message to tab:', error);
      return null;
    }
  }
  
  // Event listeners
  toggleBtn.addEventListener('click', async function() {
    try {
      const response = await sendMessageToTab({ action: 'toggle' });
      if (response) {
        updateUI(response);
      }
    } catch (error) {
      console.error('Error toggling auto clicker:', error);
    }
  });
  
  intervalInput.addEventListener('change', async function() {
    const interval = parseInt(this.value);
    if (interval >= 100 && interval <= 10000) {
      try {
        await sendMessageToTab({ 
          action: 'setInterval', 
          interval: interval 
        });
      } catch (error) {
        console.error('Error setting interval:', error);
      }
    }
  });
  
  debugModeCheckbox.addEventListener('change', async function() {
    try {
      const response = await sendMessageToTab({ 
        action: 'setDebugMode', 
        enabled: this.checked 
      });
      if (response) {
        console.log('Debug mode:', response.debugMode ? 'enabled' : 'disabled');
      }
    } catch (error) {
      console.error('Error setting debug mode:', error);
    }
  });
  
  // Debug button event listeners
  highlightBtn.addEventListener('click', async function() {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        function: () => {
          if (window.EverwingDebug) {
            window.EverwingDebug.highlightButtons();
          } else {
            console.log('Debug tools not loaded yet');
          }
        }
      });
    } catch (error) {
      console.error('Error highlighting buttons:', error);
    }
  });
  
  analyzeBtn.addEventListener('click', async function() {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        function: () => {
          if (window.EverwingDebug) {
            window.EverwingDebug.analyzePage();
          } else {
            console.log('Debug tools not loaded yet');
          }
        }
      });
    } catch (error) {
      console.error('Error analyzing page:', error);
    }
  });
  
  findBtn.addEventListener('click', async function() {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        function: () => {
          if (window.everwingAutoClicker) {
            const buttons = window.everwingAutoClicker.findButtons();
            console.log('Found buttons:', buttons);
          } else {
            console.log('Auto clicker not loaded yet');
          }
        }
      });
    } catch (error) {
      console.error('Error finding buttons:', error);
    }
  });
  
  exportBtn.addEventListener('click', async function() {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        function: () => {
          if (window.EverwingDebug) {
            window.EverwingDebug.exportState();
          } else {
            console.log('Debug tools not loaded yet');
          }
        }
      });
    } catch (error) {
      console.error('Error exporting state:', error);
    }
  });
  
  // Add keyboard shortcuts
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggleBtn.click();
    }
  });
});
